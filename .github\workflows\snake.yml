name: Generate Snake Animation

on:
  # Run automatically every 12 hours
  schedule:
    - cron: "0 */12 * * *"

  # Allows to manually run the job at any time
  workflow_dispatch:

  # Run on every push on the master branch
  push:
    branches:
    - master
    - main

jobs:
  generate:
    permissions:
      contents: write
    runs-on: ubuntu-latest
    timeout-minutes: 5

    steps:
      # Checkout repository
      - name: Checkout
        uses: actions/checkout@v4

      # Generates a snake game from a github user contributions graph and output a screen capture as animated svg or gif
      - name: Generate github-contribution-grid-snake.svg
        uses: Platane/snk/svg-only@v3
        with:
          github_user_name: vickyymosafan
          outputs: |
            dist/github-contribution-grid-snake.svg
            dist/github-contribution-grid-snake-dark.svg?palette=github-dark
            dist/github-contribution-grid-snake.gif?color_snake=orange&color_dots=#bfd6f6,#8dbdff,#64a1f4,#4b91f1,#3c7dd9
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      # Push the content of <build_dir> to a branch
      # The content will be available at https://raw.githubusercontent.com/<github_user>/<repository>/<target_branch>/<file> , or as github page
      - name: Push github-contribution-grid-snake.svg to the output branch
        uses: crazy-max/ghaction-github-pages@v4
        with:
          target_branch: output
          build_dir: dist
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
