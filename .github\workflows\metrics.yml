name: Metrics
on:
  # Schedule daily updates
  schedule: [{cron: "0 0 * * *"}]
  # (optional) Run workflow manually
  workflow_dispatch:
  # (optional) Run workflow when pushing on master/main
  push: {branches: ["master", "main"]}
jobs:
  github-metrics:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - uses: lowlighter/metrics@latest
        with:
          token: ${{ secrets.METRICS_TOKEN }}

          # Options
          user: vickyymosafan
          template: classic
          base: header, activity, community, repositories, metadata
          config_timezone: Asia/Jakarta
          
          # Plugin isocalendar
          plugin_isocalendar: yes
          plugin_isocalendar_duration: half-year
          
          # Plugin languages
          plugin_languages: yes
          plugin_languages_analysis_timeout: 15
          plugin_languages_categories: markup, programming
          plugin_languages_colors: github
          plugin_languages_limit: 8
          plugin_languages_recent_categories: markup, programming
          plugin_languages_recent_days: 14
          plugin_languages_recent_load: 300
          plugin_languages_sections: most-used
          plugin_languages_threshold: 0%
          
          # Plugin activity
          plugin_activity: yes
          plugin_activity_days: 14
          plugin_activity_filter: all
          plugin_activity_limit: 5
          plugin_activity_load: 300
          plugin_activity_visibility: all
          
          # Plugin achievements
          plugin_achievements: yes
          plugin_achievements_display: detailed
          plugin_achievements_secrets: yes
          plugin_achievements_threshold: C
          
          # Plugin notable contributions
          plugin_notable: yes
          plugin_notable_from: organization
          plugin_notable_repositories: yes
          
          # Plugin discussions
          plugin_discussions: yes
          plugin_discussions_categories: yes
          
          # Plugin followup
          plugin_followup: yes
          plugin_followup_sections: repositories
          
          # Plugin habits
          plugin_habits: yes
          plugin_habits_charts_type: classic
          plugin_habits_days: 14
          plugin_habits_facts: yes
          plugin_habits_from: 200
          
          # Plugin reactions
          plugin_reactions: yes
          plugin_reactions_limit: 200
          plugin_reactions_limit_discussions: 100
          plugin_reactions_limit_discussions_comments: 100
          plugin_reactions_limit_issues: 100
          
          # Plugin stars
          plugin_stars: yes
          plugin_stars_limit: 4
