name: Advanced GitHub Metrics

on:
  schedule:
    - cron: "0 */12 * * *"  # Runs every 12 hours for optimal performance
  workflow_dispatch:
  push:
    branches: ["master", "main"]

jobs:
  github-metrics:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Generate Comprehensive Metrics
        uses: lowlighter/metrics@latest
        with:
          token: ${{ secrets.METRICS_TOKEN }}

          # Core Configuration
          user: vickyymosafan
          template: classic
          base: header, activity, community, repositories, metadata
          config_timezone: Asia/Jakarta
          config_display: large
          config_animations: yes

          # Enhanced Calendar Plugin
          plugin_isocalendar: yes
          plugin_isocalendar_duration: full-year

          # Advanced Languages Plugin
          plugin_languages: yes
          plugin_languages_analysis_timeout: 30
          plugin_languages_categories: markup, programming
          plugin_languages_colors: github
          plugin_languages_limit: 10
          plugin_languages_recent_categories: markup, programming
          plugin_languages_recent_days: 30
          plugin_languages_recent_load: 500
          plugin_languages_sections: most-used, recently-used
          plugin_languages_threshold: 0%
          plugin_languages_details: percentage, lines, bytes-size

          # Enhanced Activity Plugin
          plugin_activity: yes
          plugin_activity_days: 30
          plugin_activity_filter: all
          plugin_activity_limit: 10
          plugin_activity_load: 500
          plugin_activity_visibility: all

          # Advanced Achievements Plugin
          plugin_achievements: yes
          plugin_achievements_display: detailed
          plugin_achievements_secrets: yes
          plugin_achievements_threshold: B
          plugin_achievements_only: polyglot, stargazer, sponsor, deployer, member, maintainer, developer, scripter, packager, explorer, infographile, manager

          # Enhanced Notable Contributions
          plugin_notable: yes
          plugin_notable_from: organization
          plugin_notable_repositories: yes
          plugin_notable_indepth: yes

          # Advanced Habits Plugin
          plugin_habits: yes
          plugin_habits_charts_type: chartist
          plugin_habits_days: 30
          plugin_habits_facts: yes
          plugin_habits_from: 400
          plugin_habits_languages_limit: 8
          plugin_habits_languages_threshold: 0%

          # Enhanced Stars Plugin
          plugin_stars: yes
          plugin_stars_limit: 6

          # Code Plugin for detailed analysis
          plugin_code: yes
          plugin_code_lines: 12
          plugin_code_load: 400
          plugin_code_days: 3
          plugin_code_visibility: public

          # Traffic Plugin
          plugin_traffic: yes
