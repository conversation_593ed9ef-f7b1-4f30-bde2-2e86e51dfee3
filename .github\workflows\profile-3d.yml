name: Advanced GitHub Profile 3D Contribution

on:
  schedule:
    - cron: "0 */6 * * *"  # Runs every 6 hours for more frequent updates
  workflow_dispatch:
  push:
    branches: ["main", "master"]

permissions:
  contents: write

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    name: generate-advanced-github-profile-3d
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Generate 3D Contribution Graph
        uses: yoshi389111/github-profile-3d-contrib@latest
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          USERNAME: ${{ github.repository_owner }}
          MAX_REPOS: 100

      - name: Generate Enhanced 3D Profile
        uses: yoshi389111/github-profile-3d-contrib@latest
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          USERNAME: ${{ github.repository_owner }}
          SETTING_JSON: '{"type": "normal", "config": {"timezone": "Asia/Jakarta"}}'

      - name: Commit and Push Changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add -A
          if git diff --staged --quiet; then
            echo "✅ No changes to commit"
          else
            git commit -m "🚀 Update 3D Profile Contribution Graph - $(date '+%Y-%m-%d %H:%M:%S')" || echo "❌ Commit failed"
            git push || echo "❌ Push failed"
          fi
