name: Advanced GitHub Profile 3D Contribution

on:
  schedule:
    - cron: "0 */6 * * *"  # Runs every 6 hours for more frequent updates
  workflow_dispatch:
  push:
    branches: ["main", "master"]

permissions:
  contents: write

jobs:
  build:
    runs-on: ubuntu-latest
    name: generate-advanced-github-profile-3d
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Generate 3D Contribution Graph
        uses: yoshi389111/github-profile-3d-contrib@latest
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          USERNAME: ${{ github.repository_owner }}
          MAX_REPOS: 100

      - name: Generate Multiple 3D Variants
        uses: yoshi389111/github-profile-3d-contrib@latest
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          USERNAME: ${{ github.repository_owner }}
          SETTING_JSON: |
            {
              "type": "normal",
              "config": {
                "timezone": "Asia/Jakarta",
                "animation": {
                  "spin": true,
                  "heart": true
                },
                "style": {
                  "backgroundColor": "#0d1117",
                  "foregroundColor": "#ffffff",
                  "strongColor": "#00d4aa",
                  "weakColor": "#161b22",
                  "radarColor": "#01ac4a",
                  "growingAnimation": true,
                  "calendarHeatmapColor": "#238636"
                }
              }
            }

      - name: Commit and Push Changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add -A
          if git diff --staged --quiet; then
            echo "No changes to commit"
          else
            git commit -m "🚀 Update 3D Profile Contribution Graph - $(date '+%Y-%m-%d %H:%M:%S')"
            git push
          fi
