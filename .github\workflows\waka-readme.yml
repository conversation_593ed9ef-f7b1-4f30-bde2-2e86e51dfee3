name: Advanced WakaTime Stats

on:
  schedule:
    - cron: '0 */8 * * *'  # Runs every 8 hours for better performance
  workflow_dispatch:

jobs:
  update-readme:
    name: Update Advanced WakaTime Metrics
    runs-on: ubuntu-latest
    steps:
      - name: Update WakaTime Stats
        uses: anmol098/waka-readme-stats@master
        with:
          WAKATIME_API_KEY: ${{ secrets.WAKATIME_API_KEY }}
          GH_TOKEN: ${{ secrets.GH_TOKEN }}

          # Enhanced Display Options
          SHOW_OS: "True"
          SHOW_PROJECTS: "True"
          SHOW_PROFILE_VIEWS: "False"
          SHOW_EDITORS: "True"
          SHOW_LANGUAGE_PER_REPO: "True"
          SHOW_LOC_CHART: "True"
          SHOW_LINES_OF_CODE: "True"
          SHOW_SHORT_INFO: "True"
          SHOW_TIMEZONE: "True"
          SHOW_LANGUAGE: "True"
          SHOW_TOTAL_CODE_TIME: "True"
          SHOW_COMMIT: "True"
          SHOW_DAYS_OF_WEEK: "True"
          SHOW_UPDATED_DATE: "True"

          # Advanced Features
          SHOW_WEEKLY_LANG: "True"
          SHOW_MONTHLY_LANG: "True"
          LOCALE: "id_ID"
          COMMIT_BY_ME: "True"
